"""
API views for the gaia_chat app.

These views provide a REST API for interacting with the chatobj functionality.
All API views require authentication.
"""

import json
import os
import logging
import asyncio
import re
from datetime import datetime
from django.http import JsonResponse, StreamingHttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from typing import Dict, Any, List, Optional

# Import chatobj components
from gaia.gaia_ceto.ceto_v002.chatobj import (
    ChatManager, Conversation, MockLLM, OpenAILLM, AnthropicLLM
)

# Import MCP client libraries if available
MCP_SSE_AVAILABLE = False
MCP_HTTP_AVAILABLE = False
FASTMCP_AVAILABLE = False

# Try to import SSE client
try:
    from gaia.gaia_ceto.proto_mcp.mcp_sse_clientlib import MCPClientLib as MCPSSEClientLib
    MCP_SSE_AVAILABLE = True
except ImportError:
    logging.warning("MCP SSE client library not available. MCP SSE provider will not work.")

# Try to import HTTP client
try:
    from gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib import MCPClientLib as MCPHTTPClientLib
    MCP_HTTP_AVAILABLE = True
except ImportError:
    logging.warning("MCP HTTP client library not available. MCP HTTP provider will not work.")

# Try to import FastMCP for progress support
try:
    from fastmcp import Client
    from fastmcp.client.logging import LogMessage
    FASTMCP_AVAILABLE = True
except ImportError:
    logging.warning("FastMCP not available. Progress display for direct tool calls will not work.")

# For backward compatibility
MCP_AVAILABLE = MCP_SSE_AVAILABLE


# Define MCPSSELLM class for web UI
class MCPSSELLM:
    """MCP SSE client implementation of the LLM interface for web UI."""

    def __init__(self, server_url: str = "http://0.0.0.0:9000/sse", model_name: str = "claude-3-5-sonnet-20240620"):
        """Initialize the MCP SSE LLM.

        Args:
            server_url: The URL of the MCP SSE server.
            model_name: The name of the Claude model to use.
        """
        if not MCP_SSE_AVAILABLE:
            raise ImportError("MCP SSE client library is not available. Cannot use MCP SSE LLM.")

        self.server_url = server_url
        self.model_name = model_name
        self.client = None
        self.messages = []
        self.loop = asyncio.new_event_loop()
        self.protocol = "SSE"  # Indicate which protocol is being used

        # Initialize the MCP client
        self.client = self._init_client()

    def _init_client(self):
        """Initialize the MCP SSE client."""
        client = MCPSSEClientLib(debug_callback=self._debug_callback)

        # Connect to the server
        logging.info(f"Connecting to MCP server via SSE at: {self.server_url}")
        success = self.loop.run_until_complete(client.connect_to_server(self.server_url))

        if not success:
            raise ConnectionError(f"Failed to connect to MCP server at {self.server_url}")

        # Log available tools
        tool_names = [tool['name'] for tool in client.available_tools]
        logging.info(f"Connected to server with tools: {', '.join(tool_names)}")

        return client

    def _debug_callback(self, level: str, message: str, data: Any = None):
        """Callback for debug messages from the client library."""
        if level == "error":
            logging.error(message)
        elif level == "warning":
            logging.warning(message)
        elif level == "info":
            logging.info(message)
        elif level == "debug":
            logging.debug(message)

    def generate_response(self, prompt: str, context: List[Dict[str, Any]], **kwargs) -> str:
        """Generate a response using the MCP SSE client.

        Args:
            prompt: The user's input text.
            context: The conversation history.
            **kwargs: Additional parameters to pass to the MCP client.

        Returns:
            The generated response.
        """
        try:
            # Note: The quote handling for direct tool calls is handled in chatobj.py

            # Convert context to the format expected by the MCP client
            # Extract system messages separately for the MCP client
            messages = []
            system_messages = []

            # Extract user and assistant messages for the messages array
            # and system messages separately
            for msg in context:
                if msg["role"] in ["user", "assistant"]:
                    messages.append({
                        "role": msg["role"],
                        "content": msg["content"]
                    })
                elif msg["role"] == "system":
                    system_messages.append(msg["content"])

            # Add system messages back to the conversation history for the MCP client
            # The MCP client will extract them and pass them as the system parameter to Anthropic
            if system_messages:
                for sys_msg in system_messages:
                    messages.insert(0, {"role": "system", "content": sys_msg})

            # Process the query
            result = self.loop.run_until_complete(self.client.process_query(
                query=prompt,
                model=self.model_name,
                max_tokens=kwargs.get("max_tokens", 1024),
                tool_timeout=kwargs.get("tool_timeout", 3600),  #  for long-running tools
                conversation_history=messages
            ))

            # Update conversation history
            self.messages = result["messages"]

            # Print any errors
            if result["error"]:
                logging.error(f"Error from MCP SSE client: {result['error']}")
                return f"Error: {result['error']}"

            # Format tool results if any
            tool_results_text = ""
            if result["tool_results"]:
                tool_calls_info = f"\nClaude requested {len(result['tool_results'])} tool call(s).\n"
                for i, tool_result in enumerate(result["tool_results"]):
                    tool_calls_info += f"\nTool Call {i+1}: '{tool_result.tool_name}'\n"
                    if tool_result.success:
                        tool_calls_info += f"Result: {tool_result.content}\n"
                    else:
                        tool_calls_info += f"Error: {tool_result.error}\n"

                tool_results_text = tool_calls_info

            # Return Claude's response with tool results
            if tool_results_text:
                return f"{tool_results_text}\n\n{result['final_text']}"
            else:
                return result["final_text"]

        except Exception as e:
            logging.error(f"Error generating response from MCP SSE: {e}")
            return f"Error generating response: {str(e)}"

    def cleanup(self):
        """Explicitly clean up resources. Should be called before object destruction."""
        if self.client:
            try:
                self.loop.run_until_complete(self.client.cleanup())
                logging.info("MCP SSE client cleaned up successfully")
            except Exception as e:
                logging.error(f"Error cleaning up MCP SSE client: {e}")
            finally:
                self.client = None

        # Close the event loop
        if self.loop and not self.loop.is_closed():
            try:
                self.loop.close()
                logging.info("Event loop closed successfully")
            except Exception as e:
                logging.error(f"Error closing event loop: {e}")

    def __del__(self):
        """Clean up resources when the object is destroyed."""
        # Only attempt cleanup if we still have resources and the loop is running
        if self.client and self.loop and not self.loop.is_closed():
            try:
                # Check if the loop is running in the current thread
                if self.loop.is_running():
                    # If the loop is running, we can't use run_until_complete
                    # Just log a warning and let the resources be cleaned up by the GC
                    logging.warning("MCP SSE client cleanup skipped - event loop is running")
                else:
                    # Safe to run cleanup
                    self.loop.run_until_complete(self.client.cleanup())
                    logging.info("MCP SSE client cleaned up in destructor")
            except Exception as e:
                logging.warning(f"Error cleaning up MCP SSE client in destructor: {e}")

        # Close the event loop if it's not already closed
        if hasattr(self, 'loop') and self.loop and not self.loop.is_closed():
            try:
                self.loop.close()
            except Exception as e:
                logging.warning(f"Error closing event loop in destructor: {e}")

    @classmethod
    def get_available_models(cls):
        """Get available models for MCP SSE.

        Returns:
            A list of available model names.
        """
        from gaia.gaia_llm.model_config import get_anthropic_models
        return get_anthropic_models()


# Define MCPHTTPLLM class for web UI
class MCPHTTPLLM:
    """MCP HTTP client implementation of the LLM interface for web UI."""

    def __init__(self, server_url: str = "http://0.0.0.0:9000/mcp", model_name: str = "claude-3-5-sonnet-20240620"):
        """Initialize the MCP HTTP LLM.

        Args:
            server_url: The URL of the MCP HTTP server.
            model_name: The name of the Claude model to use.
        """
        if not MCP_HTTP_AVAILABLE:
            raise ImportError("MCP HTTP client library is not available. Cannot use MCP HTTP LLM.")

        self.server_url = server_url
        self.model_name = model_name
        self.client = None
        self.messages = []
        self.loop = asyncio.new_event_loop()
        self.protocol = "HTTP"  # Indicate which protocol is being used

        # Initialize the MCP client
        self.client = self._init_client()

    def _init_client(self):
        """Initialize the MCP HTTP client."""
        client = MCPHTTPClientLib(debug_callback=self._debug_callback)

        # Connect to the server
        logging.info(f"Connecting to MCP server via HTTP at: {self.server_url}")
        success = self.loop.run_until_complete(client.connect_to_server(self.server_url))

        if not success:
            raise ConnectionError(f"Failed to connect to MCP server at {self.server_url}")

        # Log available tools
        tool_names = [tool['name'] for tool in client.available_tools]
        logging.info(f"Connected to server with tools: {', '.join(tool_names)}")

        return client

    def _debug_callback(self, level: str, message: str, data: Any = None):
        """Callback for debug messages from the client library."""
        if level == "error":
            logging.error(message)
        elif level == "warning":
            logging.warning(message)
        elif level == "info":
            logging.info(message)
        elif level == "debug":
            logging.debug(message)

    def generate_response(self, prompt: str, context: List[Dict[str, Any]], **kwargs) -> str:
        """Generate a response using the MCP HTTP client.

        Args:
            prompt: The user's input text.
            context: The conversation history.
            **kwargs: Additional parameters to pass to the MCP client.

        Returns:
            The generated response.
        """
        try:
            # Note: The quote handling for direct tool calls is handled in chatobj.py

            # Convert context to the format expected by the MCP client
            # Extract system messages separately for the MCP client
            messages = []
            system_messages = []

            # Extract user and assistant messages for the messages array
            # and system messages separately
            for msg in context:
                if msg["role"] in ["user", "assistant"]:
                    messages.append({
                        "role": msg["role"],
                        "content": msg["content"]
                    })
                elif msg["role"] == "system":
                    system_messages.append(msg["content"])

            # Add system messages back to the conversation history for the MCP client
            # The MCP client will extract them and pass them as the system parameter to Anthropic
            if system_messages:
                for sys_msg in system_messages:
                    messages.insert(0, {"role": "system", "content": sys_msg})

            # Process the query
            result = self.loop.run_until_complete(self.client.process_query(
                query=prompt,
                model=self.model_name,
                max_tokens=kwargs.get("max_tokens", 1024),
                tool_timeout=kwargs.get("tool_timeout", 3600),  # for long-running tools
                conversation_history=messages
            ))

            # Update conversation history
            self.messages = result["messages"]

            # Print any errors
            if result["error"]:
                logging.error(f"Error from MCP HTTP client: {result['error']}")
                return f"Error: {result['error']}"

            # Format tool results if any
            tool_results_text = ""
            if result["tool_results"]:
                tool_calls_info = f"\nClaude requested {len(result['tool_results'])} tool call(s).\n"
                for i, tool_result in enumerate(result["tool_results"]):
                    tool_calls_info += f"\nTool Call {i+1}: '{tool_result.tool_name}'\n"
                    if tool_result.success:
                        tool_calls_info += f"Result: {tool_result.content}\n"
                    else:
                        tool_calls_info += f"Error: {tool_result.error}\n"

                tool_results_text = tool_calls_info

            # Return Claude's response with tool results
            if tool_results_text:
                return f"{tool_results_text}\n\n{result['final_text']}"
            else:
                return result["final_text"]

        except Exception as e:
            logging.error(f"Error generating response from MCP HTTP: {e}")
            return f"Error generating response: {str(e)}"

    def cleanup(self):
        """Explicitly clean up resources. Should be called before object destruction."""
        if self.client:
            try:
                self.loop.run_until_complete(self.client.cleanup())
                logging.info("MCP HTTP client cleaned up successfully")
            except Exception as e:
                logging.error(f"Error cleaning up MCP HTTP client: {e}")
            finally:
                self.client = None

        # Close the event loop
        if self.loop and not self.loop.is_closed():
            try:
                self.loop.close()
                logging.info("Event loop closed successfully")
            except Exception as e:
                logging.error(f"Error closing event loop: {e}")

    def __del__(self):
        """Clean up resources when the object is destroyed."""
        # Only attempt cleanup if we still have resources and the loop is running
        if self.client and self.loop and not self.loop.is_closed():
            try:
                # Check if the loop is running in the current thread
                if self.loop.is_running():
                    # If the loop is running, we can't use run_until_complete
                    # Just log a warning and let the resources be cleaned up by the GC
                    logging.warning("MCP HTTP client cleanup skipped - event loop is running")
                else:
                    # Safe to run cleanup
                    self.loop.run_until_complete(self.client.cleanup())
                    logging.info("MCP HTTP client cleaned up in destructor")
            except Exception as e:
                logging.warning(f"Error cleaning up MCP HTTP client in destructor: {e}")

        # Close the event loop if it's not already closed
        if hasattr(self, 'loop') and self.loop and not self.loop.is_closed():
            try:
                self.loop.close()
            except Exception as e:
                logging.warning(f"Error closing event loop in destructor: {e}")

    @classmethod
    def get_available_models(cls):
        """Get available models for MCP HTTP.

        Returns:
            A list of available model names.
        """
        from gaia.gaia_llm.model_config import get_anthropic_models
        return get_anthropic_models()


# For backward compatibility
MCPLLM = MCPSSELLM

# Set up logging
logger = logging.getLogger(__name__)

# Global chat manager instance
STORAGE_DIR = "/var/lib/gaia/GAIA_FS/ceto_conversations"
os.makedirs(STORAGE_DIR, exist_ok=True)
chat_manager = ChatManager(storage_dir=STORAGE_DIR)

# Progress tracking for streaming
class ProgressTracker:
    """Track progress and info messages for streaming responses."""

    def __init__(self):
        self.progress_count = 0
        self.info_count = 0
        self.progress_updates = []

    def reset(self):
        """Reset counters and updates."""
        self.progress_count = 0
        self.info_count = 0
        self.progress_updates = []

# Helper functions for direct tool call detection and progress handling
def is_direct_tool_call(message: str) -> tuple[bool, Optional[str]]:
    """Check if the message is a direct tool call and extract the tool name.

    Args:
        message: The user's message.

    Returns:
        A tuple of (is_direct_call, tool_name).
    """
    # Check for direct tool call patterns like "long_task" or "tool_name(args)"
    message = message.strip()

    # Pattern 1: Just the tool name
    if message in ["long_task", "echostring_longrunning", "firecrawl_scrape", "firecrawl_scrape_text_only"]:
        return True, message

    # Pattern 2: Tool name with parentheses
    if re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*\(\)$', message):
        return True, message.replace('()', '')

    # Pattern 3: Tool name with arguments
    match = re.match(r'^([a-zA-Z_][a-zA-Z0-9_]*)\(.*\)$', message)
    if match:
        return True, match.group(1)

    return False, None


async def handle_direct_tool_call_with_progress(tool_name: str, server_url: str, tracker: ProgressTracker):
    """Handle a direct tool call using FastMCP with progress display.

    Args:
        tool_name: The name of the tool to call.
        server_url: The MCP server URL.
        tracker: Progress tracker instance.

    Yields:
        Server-sent event data for progress updates and final result.
    """
    if not FASTMCP_AVAILABLE:
        yield f"data: {json.dumps({'type': 'error', 'content': 'FastMCP not available. Cannot show progress for direct tool calls.'})}\n\n"
        return

    # Reset tracker
    tracker.reset()

    # Create a queue to collect progress events for yielding
    progress_queue = []

    async def progress_handler(progress: float, total: Optional[float], message: Optional[str]):
        """Handle progress updates from FastMCP client."""
        tracker.progress_count += 1

        if total:
            pct = progress / total * 100

            # Create visual progress bar
            bar_length = 28
            filled = int(bar_length * pct / 100)
            bar = "█" * filled + "░" * (bar_length - filled)

            # Format message for stdout (same as chat_term.py)
            msg_text = f" – {message}" if message else ""

            # Print to stdout for runserver.py visibility (same format as chat_term.py)
            print(f"📊 [{bar}] {pct:5.1f}% {int(progress)}/{int(total)}{msg_text}", flush=True)

            # Create progress event for browser console (same format as terminal)
            progress_event = {
                'type': 'progress',
                'progress': int(progress),
                'total': int(total),
                'percentage': pct,
                'bar': bar,
                'message': message or '',
                'console_message': f"📊 [{bar}] {pct:5.1f}% {int(progress)}/{int(total)}{msg_text}"
            }

            # Add to queue for immediate yielding
            progress_queue.append(progress_event)

            # Store progress data for later summary
            tracker.progress_updates.append(progress_event)
        else:
            # Handle progress without total
            console_msg = f"📊 Step {progress} – {message or ''}"

            # Print to stdout for runserver.py visibility (same format as chat_term.py)
            print(console_msg, flush=True)

            # Create progress event for browser console
            progress_event = {
                'type': 'progress',
                'progress': int(progress),
                'total': None,
                'percentage': None,
                'bar': None,
                'message': message or f'Step {int(progress)}',
                'console_message': console_msg
            }

            # Add to queue for immediate yielding
            progress_queue.append(progress_event)

            # Store progress data for later summary
            tracker.progress_updates.append(progress_event)

    async def log_handler(log_message):
        """Handle log messages from FastMCP client."""
        level = log_message.level.upper()

        # Count info messages but don't display them for cleaner output
        if level == "INFO":
            tracker.info_count += 1

        # Only display non-info messages (errors, warnings, etc.)
        if level != "INFO":
            logger_name = log_message.logger or "server"
            payload = log_message.data
            emoji = {"ERROR": "❌", "WARNING": "⚠️", "DEBUG": "🔍"}.get(level, "ℹ️")

            # Print to stdout for runserver.py visibility (same format as chat_term.py)
            print(f"{emoji} [{level}] {logger_name}: {payload}", flush=True)

    # Print to stdout for runserver.py visibility
    print(f"🚀 Calling {tool_name} with progress display...", flush=True)
    yield f"data: {json.dumps({'type': 'start', 'content': f'🚀 Calling {tool_name} with progress display...'})}\n\n"

    try:
        client = Client(
            server_url,
            progress_handler=progress_handler,
            log_handler=log_handler,
        )

        async with client:
            # Print to stdout for runserver.py visibility
            print("✅ Connected (FastMCP client with progress)", flush=True)
            yield f"data: {json.dumps({'type': 'info', 'content': '✅ Connected (FastMCP client with progress)'})}\n\n"

            # Call the tool - progress and info will be handled by callbacks
            # We need to yield progress events as they come in
            import asyncio

            # Create a task for the tool call
            tool_task = asyncio.create_task(client.call_tool(tool_name))

            # Poll for progress events while the tool is running
            while not tool_task.done():
                # Yield any queued progress events
                while progress_queue:
                    progress_event = progress_queue.pop(0)
                    yield f"data: {json.dumps(progress_event)}\n\n"

                # Small delay to avoid busy waiting
                await asyncio.sleep(0.1)

            # Get the result
            result = await tool_task

            # Yield any remaining progress events
            while progress_queue:
                progress_event = progress_queue.pop(0)
                yield f"data: {json.dumps(progress_event)}\n\n"

        # Print final summary to stdout for runserver.py visibility
        print(f"🎉 Tool finished successfully", flush=True)
        print(f"📊 Progress msgs: {tracker.progress_count}   ℹ️ Info msgs: {tracker.info_count}", flush=True)

        # Final summary for frontend
        summary_data = {
            'type': 'summary',
            'progress_count': tracker.progress_count,
            'info_count': tracker.info_count,
            'content': '🎉 Tool finished successfully'
        }
        yield f"data: {json.dumps(summary_data)}\n\n"

        # Yield the result content if available
        if hasattr(result, 'content') and result.content:
            result_content = str(result.content[0].text if hasattr(result.content[0], 'text') else result.content[0])
            yield f"data: {json.dumps({'type': 'result', 'content': result_content})}\n\n"
        else:
            yield f"data: {json.dumps({'type': 'result', 'content': 'Tool completed successfully'})}\n\n"

    except Exception as e:
        error_data = {
            'type': 'error',
            'content': f'❌ Error calling {tool_name}: {e}'
        }
        yield f"data: {json.dumps(error_data)}\n\n"


@require_http_methods(["GET"])
@csrf_exempt
def test_progress_no_auth(request):
    """Test endpoint to verify progress appears in runserver stdout (no authentication required)."""

    def generate_progress():
        """Generate progress events for testing."""

        # Print a clear marker to stdout
        print("=" * 60, flush=True)
        print("🧪 TEST PROGRESS ENDPOINT CALLED - CHECK RUNSERVER STDOUT", flush=True)
        print("=" * 60, flush=True)

        yield f"data: {json.dumps({'type': 'start', 'content': 'Starting test progress...'})}\n\n"

        # Test the progress function directly
        tracker = ProgressTracker()

        try:
            import asyncio

            async def run_test():
                print("🚀 About to call handle_direct_tool_call_with_progress...", flush=True)

                async for event in handle_direct_tool_call_with_progress(
                    "long_task",
                    "http://localhost:9000/mcp",
                    tracker
                ):
                    yield event

                print("✅ handle_direct_tool_call_with_progress completed", flush=True)

            # Run the async generator
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            async def collect_events():
                events = []
                async for event in run_test():
                    events.append(event)
                    yield event
                    if len(events) >= 10:  # Limit events
                        break

                print(f"📊 Collected {len(events)} events total", flush=True)
                yield f"data: {json.dumps({'type': 'complete', 'content': f'Test completed with {len(events)} events'})}\n\n"

            # Convert async generator to sync generator for Django response
            import asyncio

            async def async_to_sync():
                async for event in collect_events():
                    yield event

            # This is a bit complex - let's simplify and just test the basic progress
            yield f"data: {json.dumps({'type': 'info', 'content': 'Testing basic progress display...'})}\n\n"

            # Test basic progress display
            print("📊 [████████████████████████████] 100.0% 1/1 – TEST PROGRESS MESSAGE", flush=True)
            print("🎉 Test progress display completed", flush=True)

            yield f"data: {json.dumps({'type': 'result', 'content': 'Test completed - check runserver stdout for progress bars!'})}\n\n"

        except Exception as e:
            print(f"❌ Error in test: {e}", flush=True)
            yield f"data: {json.dumps({'type': 'error', 'content': f'Test error: {e}'})}\n\n"

    response = StreamingHttpResponse(generate_progress(), content_type='text/event-stream')
    response['Cache-Control'] = 'no-cache'
    return response

# LLM factory function
def create_llm(llm_type="mock", model_name=None):
    """Create an LLM instance based on the specified type.

    Args:
        llm_type: The type of LLM to create ("mock", "openai", "anthropic", "mcp", or "mcp-http").
        model_name: The specific model name to use (if applicable).
                   For MCP providers, this can be a server URL.

    Returns:
        An instance of the specified LLM.
    """
    kwargs = {}
    if model_name:
        kwargs["model_name"] = model_name

    if llm_type.lower() == "mock":
        return MockLLM()
    elif llm_type.lower() == "openai":
        try:
            return OpenAILLM(**kwargs)
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI LLM: {e}")
            return MockLLM()
    elif llm_type.lower() == "anthropic":
        try:
            return AnthropicLLM(**kwargs)
        except Exception as e:
            logger.error(f"Failed to initialize Anthropic LLM: {e}")
            return MockLLM()
    elif llm_type.lower() == "mcp":
        if not MCP_SSE_AVAILABLE:
            logger.error("MCP SSE client library is not available. Cannot use MCP SSE LLM.")
            return MockLLM()

        try:
            # Default server URL if not specified in model_name
            server_url = "http://0.0.0.0:9000/sse"

            # If model_name contains a URL, use it as the server URL
            if model_name and "://" in model_name:
                server_url = model_name
                # Reset model_name to None to use the default model
                kwargs = {}

            return MCPSSELLM(server_url=server_url, **kwargs)
        except Exception as e:
            logger.error(f"Failed to initialize MCP SSE LLM: {e}")
            return MockLLM()
    elif llm_type.lower() == "mcp-http":
        if not MCP_HTTP_AVAILABLE:
            logger.error("MCP HTTP client library is not available. Cannot use MCP HTTP LLM.")
            return MockLLM()

        try:
            # Default server URL if not specified in model_name
            server_url = "http://0.0.0.0:9000/mcp"

            # If model_name contains a URL, use it as the server URL
            if model_name and "://" in model_name:
                server_url = model_name
                # Reset model_name to None to use the default model
                kwargs = {}

            return MCPHTTPLLM(server_url=server_url, **kwargs)
        except Exception as e:
            logger.error(f"Failed to initialize MCP HTTP LLM: {e}")
            return MockLLM()
    else:
        logger.warning(f"Unsupported LLM type: {llm_type}. Using MockLLM instead.")
        return MockLLM()

# API endpoints
@login_required
@require_http_methods(["GET"])
def get_llm_providers(request):
    """Get available LLM providers."""
    providers = [
        {"id": "mock", "name": "Mock LLM"},
        {"id": "openai", "name": "OpenAI"},
        {"id": "anthropic", "name": "Anthropic"}
    ]

    # Add MCP SSE provider if available
    if MCP_SSE_AVAILABLE:
        providers.append({"id": "mcp", "name": "MCP SSE (Tools)"})

    # Add MCP HTTP provider if available
    if MCP_HTTP_AVAILABLE:
        providers.append({"id": "mcp-http", "name": "MCP HTTP (Tools)"})

    return JsonResponse({"providers": providers})

@login_required
@require_http_methods(["GET"])
def get_models(request):
    """Get available models for a specific LLM provider."""
    provider = request.GET.get("provider", "mock")

    if provider == "mock":
        models = [{"id": "default", "name": "Default Mock"}]
    elif provider == "openai":
        try:
            models = [{"id": model, "name": model} for model in OpenAILLM.get_available_models()]
        except Exception as e:
            logger.error(f"Error getting OpenAI models: {e}")
            models = [
                {"id": "gpt-3.5-turbo", "name": "GPT-3.5 Turbo"},
                {"id": "gpt-4", "name": "GPT-4"}
            ]
    elif provider == "anthropic":
        try:
            from gaia.gaia_llm.model_config import get_default_model
            anthropic_models = AnthropicLLM.get_available_models()
            default_model = get_default_model("anthropic")
            models = [
                {
                    "id": model,
                    "name": model,
                    "is_default": model == default_model
                }
                for model in anthropic_models
            ]
        except Exception as e:
            logger.error(f"Error getting Anthropic models: {e}")
            from gaia.gaia_llm.model_config import model_registry
            models = model_registry.get_model_choices_by_provider("anthropic")
            # Add default flag to fallback models
            from gaia.gaia_llm.model_config import get_default_model
            default_model = get_default_model("anthropic")
            for model in models:
                model["is_default"] = model["id"] == default_model
    elif provider == "mcp":
        if MCP_SSE_AVAILABLE:
            try:
                # Get models from MCPSSELLM
                mcp_models = MCPSSELLM.get_available_models()
                models = [{"id": model, "name": model} for model in mcp_models]

                # Add server URL option
                models.append({
                    "id": "http://0.0.0.0:9000/sse",
                    "name": "Default MCP SSE Server (http://0.0.0.0:9000/sse)"
                })
            except Exception as e:
                logger.error(f"Error getting MCP SSE models: {e}")
                models = [
                    {"id": "claude-3-5-sonnet-20240620", "name": "Claude 3.5 Sonnet"},
                    {"id": "http://0.0.0.0:9000/sse", "name": "Default MCP SSE Server"}
                ]
        else:
            models = []
            logger.warning("MCP SSE is not available. Cannot get MCP SSE models.")
    elif provider == "mcp-http":
        if MCP_HTTP_AVAILABLE:
            try:
                # Get models from MCPHTTPLLM
                mcp_models = MCPHTTPLLM.get_available_models()
                models = [{"id": model, "name": model} for model in mcp_models]

                # Add server URL option
                models.append({
                    "id": "http://0.0.0.0:9000/mcp",
                    "name": "Default MCP HTTP Server (http://0.0.0.0:9000/mcp)"
                })
            except Exception as e:
                logger.error(f"Error getting MCP HTTP models: {e}")
                models = [
                    {"id": "claude-3-5-sonnet-20240620", "name": "Claude 3.5 Sonnet"},
                    {"id": "http://0.0.0.0:9000/mcp", "name": "Default MCP HTTP Server"}
                ]
        else:
            models = []
            logger.warning("MCP HTTP is not available. Cannot get MCP HTTP models.")
    else:
        models = []

    return JsonResponse({"models": models})

@login_required
@require_http_methods(["GET"])
def get_default_model(request):
    """Get the default model for a specific LLM provider."""
    provider = request.GET.get("provider", "mock")

    try:
        from gaia.gaia_llm.model_config import get_default_model
        default_model = get_default_model(provider)
        return JsonResponse({"default_model": default_model})
    except Exception as e:
        logger.error(f"Error getting default model for {provider}: {e}")
        # Fallback defaults
        fallback_defaults = {
            "anthropic": "claude-3-5-sonnet-20241022",
            "openai": "gpt-4o",
            "mock": "default",
            "mcp": "claude-3-5-sonnet-20241022",
            "mcp-http": "claude-3-5-sonnet-20241022"
        }
        return JsonResponse({"default_model": fallback_defaults.get(provider)})

@login_required
@require_http_methods(["POST"])
@csrf_exempt
def set_llm(request):
    """Set the LLM for the chat manager."""
    try:
        data = json.loads(request.body)
        provider = data.get("provider", "mock")
        model = data.get("model")

        # Clean up the old LLM if it's an MCP client
        global chat_manager
        if hasattr(chat_manager, 'llm') and chat_manager.llm:
            old_llm = chat_manager.llm
            if isinstance(old_llm, (MCPSSELLM, MCPHTTPLLM)) and hasattr(old_llm, 'cleanup'):
                try:
                    old_llm.cleanup()
                    logger.info(f"Cleaned up old {type(old_llm).__name__} instance")
                except Exception as e:
                    logger.warning(f"Error cleaning up old LLM: {e}")

        # Create the new LLM
        llm = create_llm(provider, model)

        # Update the chat manager
        chat_manager.llm = llm

        return JsonResponse({
            "success": True,
            "provider": provider,
            "model": model
        })
    except Exception as e:
        logger.error(f"Error setting LLM: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e)
        }, status=400)

@login_required
@require_http_methods(["GET"])
def list_conversations(request):
    """List available conversations for the authenticated user."""
    try:
        # Get the username of the authenticated user
        user_id = request.user.username

        # Filter conversations by user_id
        conversations = chat_manager.list_conversations(user_id=user_id)

        # Format the conversations for the frontend
        formatted = []
        for conv in conversations:
            formatted.append({
                "id": conv["conversation_id"],
                "title": conv["title"],
                "created_at": conv["created_at"],
                "message_count": conv["message_count"],
                "path": conv["relative_path"]
            })

        return JsonResponse({"conversations": formatted})
    except Exception as e:
        logger.error(f"Error listing conversations: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e)
        }, status=500)

def _format_context_data(context_data: Dict[str, Any], context_name: str = "context") -> str:
    """Format context data into a system message for the LLM.

    This uses the same formatting logic as chat_term.py for consistency.

    Args:
        context_data: The context data dictionary.
        context_name: The name/identifier for this context.

    Returns:
        A formatted string to be used as a system message.
    """
    # Start with basic information
    message_parts = [
        f"CONTEXT LOADED: {context_name}",
        f"Type: {context_data.get('type', 'unknown')}",
        f"Title: {context_data.get('title', 'No title')}",
        f"Description: {context_data.get('description', 'No description')}"
    ]

    # Add the actual data
    if 'data' in context_data:
        data = context_data['data']
        if isinstance(data, list):
            message_parts.append(f"\nData ({len(data)} items):")
            for i, item in enumerate(data, 1):
                if isinstance(item, dict):
                    # Format dictionary items nicely
                    item_str = f"  {i}. "
                    if 'name' in item:
                        item_str += f"Name: {item['name']}"
                    elif 'title' in item:
                        item_str += f"Title: {item['title']}"
                    elif 'id' in item:
                        item_str += f"ID: {item['id']}"

                    # Add other key fields
                    for key, value in item.items():
                        if key not in ['name', 'title', 'id']:
                            item_str += f", {key}: {value}"

                    message_parts.append(item_str)
                else:
                    message_parts.append(f"  {i}. {item}")
        elif isinstance(data, dict):
            message_parts.append("\nData:")
            for key, value in data.items():
                message_parts.append(f"  {key}: {value}")
        else:
            message_parts.append(f"\nData: {data}")

    message_parts.append("\nThis contextual information is now available for reference in our conversation.")

    return "\n".join(message_parts)

@login_required
@require_http_methods(["POST"])
@csrf_exempt
def create_conversation(request):
    """Create a new conversation for the authenticated user."""
    try:
        data = json.loads(request.body)
        # Use the provided title or generate a default one
        title = data.get("title")
        context_data = data.get("context")  # New: accept context data

        # If context data is provided and has a title, use it as the conversation title
        if context_data and context_data.get("title") and not title:
            title = context_data.get("title")
        elif not title:
            title = f"Conversation {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

        # Always use the authenticated user's username
        user_id = request.user.username

        # Create the conversation
        conversation = chat_manager.create_conversation(
            title=title,
            user_id=user_id
        )

        # Add context as system message if provided
        if context_data and context_data.get("data"):
            logger.info(f"Loading context data for conversation {conversation.conversation_id}")
            context_message = _format_context_data(context_data, "frontend_context")
            chat_manager.add_message("system", context_message)
        else:
            # Add a default system message, but only if not using MCP
            # (MCP/Anthropic API handles system messages differently)
            if not (isinstance(chat_manager.llm, MCPSSELLM) or isinstance(chat_manager.llm, MCPHTTPLLM)):
                chat_manager.add_message("system", "Welcome to Gaia Chat! How can I help you today?")

        # Save the conversation
        chat_manager.save_conversation()

        return JsonResponse({
            "success": True,
            "conversation": {
                "id": conversation.conversation_id,
                "title": conversation.title,
                "created_at": conversation.created_at
            }
        })
    except Exception as e:
        logger.error(f"Error creating conversation: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e)
        }, status=500)

@login_required
@require_http_methods(["GET"])
def load_conversation(request, conversation_id):
    """Load a conversation by ID, ensuring it belongs to the authenticated user."""
    try:
        # Load the conversation
        conversation = chat_manager.load_conversation(conversation_id)

        if not conversation:
            return JsonResponse({
                "success": False,
                "error": f"Conversation {conversation_id} not found"
            }, status=404)

        # Check if the conversation belongs to the authenticated user
        if conversation.user_id != request.user.username:
            return JsonResponse({
                "success": False,
                "error": "You do not have permission to access this conversation"
            }, status=403)

        # Set as active conversation
        chat_manager.active_conversation = conversation

        # Format the messages
        messages = []
        for msg in conversation.messages:
            messages.append({
                "role": msg["role"],
                "content": msg["content"],
                "timestamp": msg.get("timestamp", "")
            })

        return JsonResponse({
            "success": True,
            "conversation": {
                "id": conversation.conversation_id,
                "title": conversation.title,
                "created_at": conversation.created_at,
                "messages": messages
            }
        })
    except Exception as e:
        logger.error(f"Error loading conversation: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e)
        }, status=500)

@login_required
@require_http_methods(["DELETE"])
@csrf_exempt
def delete_conversation(request, conversation_id):
    """Delete a conversation, ensuring it belongs to the authenticated user."""
    try:
        # Load the conversation first to check ownership
        conversation = chat_manager.load_conversation(conversation_id)

        if not conversation:
            return JsonResponse({
                "success": False,
                "error": f"Conversation {conversation_id} not found"
            }, status=404)

        # Check if the conversation belongs to the authenticated user
        if conversation.user_id != request.user.username:
            return JsonResponse({
                "success": False,
                "error": "You do not have permission to delete this conversation"
            }, status=403)

        # Delete the conversation
        if chat_manager.delete_conversation(conversation_id):
            return JsonResponse({
                "success": True,
                "message": "Conversation deleted successfully"
            })
        else:
            return JsonResponse({
                "success": False,
                "error": "Failed to delete conversation"
            }, status=500)
    except Exception as e:
        logger.error(f"Error deleting conversation: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e)
        }, status=500)

@login_required
@require_http_methods(["POST"])
@csrf_exempt
def update_conversation(request, conversation_id):
    """Update a conversation with new messages, ensuring it belongs to the authenticated user."""
    try:
        data = json.loads(request.body)
        messages = data.get("messages", [])

        if not isinstance(messages, list):
            return JsonResponse({
                "success": False,
                "error": "Messages must be a list"
            }, status=400)

        # Load the conversation first to check ownership
        conversation = chat_manager.load_conversation(conversation_id)

        if not conversation:
            return JsonResponse({
                "success": False,
                "error": f"Conversation {conversation_id} not found"
            }, status=404)

        # Check if the conversation belongs to the authenticated user
        if conversation.user_id != request.user.username:
            return JsonResponse({
                "success": False,
                "error": "You do not have permission to update this conversation"
            }, status=403)

        # Set as active conversation
        chat_manager.active_conversation = conversation

        # Update the conversation messages
        conversation.messages = messages

        # Save the conversation
        chat_manager.save_conversation()

        return JsonResponse({
            "success": True,
            "message": "Conversation updated successfully",
            "conversation": {
                "id": conversation.conversation_id,
                "title": conversation.title,
                "message_count": len(conversation.messages)
            }
        })
    except Exception as e:
        logger.error(f"Error updating conversation: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e)
        }, status=500)

@login_required
@require_http_methods(["POST"])
@csrf_exempt
def send_message(request):
    """Send a message to the active conversation, ensuring it belongs to the authenticated user."""
    try:
        data = json.loads(request.body)

        # Handle both old format (message) and new format (messages + conversation_id)
        message = data.get("message", "")
        messages = data.get("messages", [])
        conversation_id = data.get("conversation_id")
        provider = data.get("provider")

        # If we have messages and conversation_id, use the new format
        if messages and conversation_id:
            # Load the conversation
            conversation = chat_manager.load_conversation(conversation_id)

            if not conversation:
                return JsonResponse({
                    "success": False,
                    "error": f"Conversation {conversation_id} not found"
                }, status=404)

            # Check if the conversation belongs to the authenticated user
            if conversation.user_id != request.user.username:
                return JsonResponse({
                    "success": False,
                    "error": "You do not have permission to send messages to this conversation"
                }, status=403)

            # Set as active conversation
            chat_manager.active_conversation = conversation

            # Get the last user message from the messages array
            user_messages = [msg for msg in messages if msg.get("role") == "user"]
            if not user_messages:
                return JsonResponse({
                    "success": False,
                    "error": "No user message found in messages"
                }, status=400)

            message = user_messages[-1].get("content", "")

            # Update the conversation with all messages (including context)
            # But exclude the last user message since process_message will add it
            conversation.messages = messages[:-1]  # Remove the last user message

        # Use old format if no messages/conversation_id provided
        if not message.strip():
            return JsonResponse({
                "success": False,
                "error": "Message cannot be empty"
            }, status=400)

        # Check if there's an active conversation
        if not chat_manager.active_conversation:
            return JsonResponse({
                "success": False,
                "error": "No active conversation",
                "code": "no_active_conversation"
            }, status=400)

        # Check if the active conversation belongs to the authenticated user
        if chat_manager.active_conversation.user_id != request.user.username:
            return JsonResponse({
                "success": False,
                "error": "You do not have permission to send messages to this conversation"
            }, status=403)

        # Process the message
        # Note: The quote handling for direct tool calls is now handled in chatobj.py
        # The process_message method will add the user message and generate a response
        response = chat_manager.process_message(message)

        # Save the conversation
        chat_manager.save_conversation()

        return JsonResponse({
            "success": True,
            "message": response  # Use "message" key to match frontend expectation
        })
    except ValueError as e:
        # This is likely due to no active conversation
        logger.error(f"Error sending message: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e),
            "code": "no_active_conversation"
        }, status=400)
    except Exception as e:
        logger.error(f"Error sending message: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e)
        }, status=500)


@login_required
@require_http_methods(["POST"])
@csrf_exempt
def send_message_stream(request):
    """Send a message with streaming progress updates for long-running tasks."""
    try:
        data = json.loads(request.body)
        message = data.get("message", "")

        if not message.strip():
            return JsonResponse({
                "success": False,
                "error": "Message cannot be empty"
            }, status=400)

        # Check if there's an active conversation
        if not chat_manager.active_conversation:
            return JsonResponse({
                "success": False,
                "error": "No active conversation",
                "code": "no_active_conversation"
            }, status=400)

        # Check if the active conversation belongs to the authenticated user
        if chat_manager.active_conversation.user_id != request.user.username:
            return JsonResponse({
                "success": False,
                "error": "You do not have permission to send messages to this conversation"
            }, status=403)

        # Check if this is an MCP provider that supports streaming
        is_mcp_provider = isinstance(chat_manager.llm, (MCPSSELLM, MCPHTTPLLM))

        if not is_mcp_provider:
            # Fall back to regular send_message for non-MCP providers
            return send_message(request)

        # Check if this is a direct tool call - only use streaming for direct tool calls
        is_direct_call, tool_name = is_direct_tool_call(message)

        if not (is_direct_call and tool_name and FASTMCP_AVAILABLE):
            # Fall back to regular send_message for non-direct tool calls
            return send_message(request)

        async def generate_stream():
            """Async generator function for streaming response."""
            try:
                # At this point we know it's a direct tool call with FastMCP available
                _, tool_name = is_direct_tool_call(message)

                # Use FastMCP for real progress display
                yield f"data: {json.dumps({'type': 'info', 'content': f'🔍 Detected direct tool call: {tool_name}'})}\n\n"
                yield f"data: {json.dumps({'type': 'info', 'content': '📊 Using FastMCP for progress display...'})}\n\n"

                # Determine server URL based on current LLM provider
                if isinstance(chat_manager.llm, MCPSSELLM):
                    server_url = chat_manager.llm.server_url
                elif isinstance(chat_manager.llm, MCPHTTPLLM):
                    server_url = chat_manager.llm.server_url
                else:
                    # Default to HTTP server
                    server_url = "http://localhost:9000/mcp"

                # Create progress tracker
                tracker = ProgressTracker()

                # Handle direct tool call with progress
                result_content = None
                async for progress_event in handle_direct_tool_call_with_progress(tool_name, server_url, tracker):
                    yield progress_event

                    # Extract result from result event if available
                    if '"type": "result"' in progress_event:
                        try:
                            import json as json_module
                            event_data = json_module.loads(progress_event.split('data: ')[1].split('\n')[0])
                            if event_data.get('type') == 'result':
                                result_content = event_data.get('content')
                        except:
                            pass  # Ignore parsing errors

                # Add the conversation messages
                try:
                    # Add the user message to conversation
                    chat_manager.add_message("user", message)

                    # Use the result from the tool call
                    if result_content:
                        final_response = result_content
                    else:
                        final_response = f"Tool '{tool_name}' completed successfully"

                    chat_manager.add_message("assistant", final_response)

                    # Save the conversation
                    chat_manager.save_conversation()

                    # Send final result
                    yield f"data: {json.dumps({'type': 'final', 'content': final_response})}\n\n"
                    yield f"data: {json.dumps({'type': 'complete'})}\n\n"

                except Exception as e:
                    logger.error(f"Error processing direct tool call: {e}")
                    yield f"data: {json.dumps({'type': 'error', 'content': str(e)})}\n\n"

            except Exception as e:
                logger.error(f"Error in generate_stream: {e}")
                yield f"data: {json.dumps({'type': 'error', 'content': str(e)})}\n\n"

        # Create a sync wrapper for the async generator
        def sync_stream_wrapper():
            """Sync wrapper for async generator."""
            import asyncio
            import queue
            import threading

            # Use a queue to pass data between async and sync contexts
            result_queue = queue.Queue()
            exception_holder = [None]

            def run_async_in_thread():
                """Run the async generator in a separate thread."""
                try:
                    # Create a new event loop for this thread
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    async def async_runner():
                        try:
                            async for item in generate_stream():
                                result_queue.put(('data', item))
                        except Exception as e:
                            exception_holder[0] = e
                        finally:
                            result_queue.put(('done', None))

                    loop.run_until_complete(async_runner())
                    loop.close()
                except Exception as e:
                    exception_holder[0] = e
                    result_queue.put(('done', None))

            # Start the async generator in a separate thread
            thread = threading.Thread(target=run_async_in_thread)
            thread.start()

            # Yield results from the queue
            while True:
                try:
                    msg_type, item = result_queue.get(timeout=1)
                    if msg_type == 'done':
                        break
                    elif msg_type == 'data':
                        yield item
                except queue.Empty:
                    # Check if there's an exception
                    if exception_holder[0]:
                        raise exception_holder[0]
                    continue

            # Wait for thread to complete
            thread.join()

            # Check for any final exceptions
            if exception_holder[0]:
                raise exception_holder[0]

        # Return streaming response
        response = StreamingHttpResponse(
            sync_stream_wrapper(),
            content_type='text/event-stream'
        )
        response['Cache-Control'] = 'no-cache'
        response['Access-Control-Allow-Origin'] = '*'
        return response

    except Exception as e:
        logger.error(f"Error in send_message_stream: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e)
        }, status=500)


@login_required
@require_http_methods(["GET"])
def chat_stream_get(request):
    """Handle EventSource streaming for chat messages via GET request."""
    try:
        # Get parameters from query string
        conversation_id = request.GET.get('conversation_id')
        # provider = request.GET.get('provider', 'openai')  # Not used yet

        if not conversation_id:
            return JsonResponse({
                "success": False,
                "error": "conversation_id is required"
            }, status=400)

        # Load the conversation to check ownership
        conversation = chat_manager.load_conversation(conversation_id)

        if not conversation:
            return JsonResponse({
                "success": False,
                "error": f"Conversation {conversation_id} not found"
            }, status=404)

        # Check if the conversation belongs to the authenticated user
        if conversation.user_id != request.user.username:
            return JsonResponse({
                "success": False,
                "error": "You do not have permission to access this conversation"
            }, status=403)

        # Set as active conversation
        chat_manager.active_conversation = conversation

        def generate_stream():
            """Generator function for streaming response."""
            try:
                yield f"data: {json.dumps({'type': 'ready', 'content': 'Stream ready for messages'})}\n\n"

                # Check if there's a pending streaming message to process
                import threading

                if hasattr(threading, 'local') and hasattr(threading.local(), 'user_message'):
                    user_message = threading.local().user_message

                    # Check if this is a direct tool call
                    is_direct_call, tool_name = is_direct_tool_call(user_message)

                    if (is_direct_call and tool_name and
                        isinstance(chat_manager.llm, (MCPSSELLM, MCPHTTPLLM)) and
                        FASTMCP_AVAILABLE):

                        # Determine server URL
                        if isinstance(chat_manager.llm, MCPSSELLM):
                            server_url = chat_manager.llm.server_url
                        elif isinstance(chat_manager.llm, MCPHTTPLLM):
                            server_url = chat_manager.llm.server_url
                        else:
                            server_url = "http://localhost:9000/mcp"

                        # Create progress tracker
                        tracker = ProgressTracker()

                        # Stream the progress events
                        import asyncio
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)

                        try:
                            async def stream_progress():
                                async for progress_event in handle_direct_tool_call_with_progress(tool_name, server_url, tracker):
                                    yield progress_event

                            # Convert async generator to sync
                            async_gen = stream_progress()
                            while True:
                                try:
                                    event = loop.run_until_complete(async_gen.__anext__())
                                    yield event
                                except StopAsyncIteration:
                                    break
                        finally:
                            loop.close()

                        # Add user message and final response to conversation
                        chat_manager.add_message("user", user_message)
                        chat_manager.add_message("assistant", f"Tool '{tool_name}' completed successfully")
                        chat_manager.save_conversation()

                        yield f"data: {json.dumps({'type': 'complete'})}\n\n"
                        return

                # Fallback: keep connection alive with heartbeats
                import time
                for i in range(30):  # Limit heartbeats to avoid infinite loop
                    time.sleep(1)
                    yield f"data: {json.dumps({'type': 'heartbeat'})}\n\n"

            except Exception as e:
                logger.error(f"Error in generate_stream: {e}")
                yield f"data: {json.dumps({'type': 'error', 'content': str(e)})}\n\n"

        # Return streaming response
        response = StreamingHttpResponse(
            generate_stream(),
            content_type='text/event-stream'
        )
        response['Cache-Control'] = 'no-cache'
        response['Access-Control-Allow-Origin'] = '*'
        return response

    except Exception as e:
        logger.error(f"Error in chat_stream_get: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e)
        }, status=500)


@login_required
@require_http_methods(["POST"])
@csrf_exempt
def chat_stream_init(request):
    """Initialize a chat stream with a message."""
    try:
        data = json.loads(request.body)
        conversation_id = data.get("conversation_id")
        messages = data.get("messages", [])
        # provider = data.get("provider", "openai")  # Not used yet

        if not conversation_id:
            return JsonResponse({
                "success": False,
                "error": "conversation_id is required"
            }, status=400)

        if not messages:
            return JsonResponse({
                "success": False,
                "error": "messages are required"
            }, status=400)

        # Load the conversation to check ownership
        conversation = chat_manager.load_conversation(conversation_id)

        if not conversation:
            return JsonResponse({
                "success": False,
                "error": f"Conversation {conversation_id} not found"
            }, status=404)

        # Check if the conversation belongs to the authenticated user
        if conversation.user_id != request.user.username:
            return JsonResponse({
                "success": False,
                "error": "You do not have permission to access this conversation"
            }, status=403)

        # Set as active conversation
        chat_manager.active_conversation = conversation

        # Get the last user message
        user_message = None
        for msg in reversed(messages):
            if msg.get("role") == "user":
                user_message = msg.get("content", "")
                break

        if not user_message:
            return JsonResponse({
                "success": False,
                "error": "No user message found"
            }, status=400)

        # Check if this is a direct tool call and we can use FastMCP progress
        is_direct_call, tool_name = is_direct_tool_call(user_message)

        if (is_direct_call and tool_name and
            isinstance(chat_manager.llm, (MCPSSELLM, MCPHTTPLLM)) and
            FASTMCP_AVAILABLE):

            # Use streaming with progress for direct tool calls
            # Store the message in a global variable so the streaming endpoint can access it
            import threading
            streaming_data = threading.local()
            streaming_data.user_message = user_message
            streaming_data.conversation_id = conversation_id

            return JsonResponse({
                "success": True,
                "streaming": True,
                "message": "Direct tool call detected - check EventSource for progress"
            })
        else:
            # Process the message normally for non-direct tool calls
            response = chat_manager.process_message(user_message)

            # Save the conversation
            chat_manager.save_conversation()

            return JsonResponse({
                "success": True,
                "response": response
            })

    except Exception as e:
        logger.error(f"Error in chat_stream_init: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e)
        }, status=500)
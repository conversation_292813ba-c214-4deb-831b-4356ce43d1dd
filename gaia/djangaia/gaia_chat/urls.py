from django.urls import path
from django.contrib.auth import views as auth_views
from . import views
from . import api

app_name = 'gaia_chat'

urlpatterns = [
    # Main Vue.js app view (requires authentication)
    path('', views.chat_app, name='chat_app'),
    path('companies/', views.companies_chat_view, name='companies_chat_view'),

    # Authentication URLs
    path('login/', views.login_view, name='login'),
    path('logout/', views.logout_view, name='logout'),
    path('register/', views.register_view, name='register'),
    path('profile/', views.profile_view, name='profile'),
    path('profile/change-password/', views.change_password_view, name='change_password'),

    # Password reset URLs
    path('password-reset/',
        auth_views.PasswordResetView.as_view(
            template_name='gaia_chat/auth/password_reset.html',
            email_template_name='gaia_chat/auth/password_reset_email.html',
            subject_template_name='gaia_chat/auth/password_reset_subject.txt',
            success_url='/gaia_chat/password-reset/done/'
        ),
        name='password_reset'
    ),
    path('password-reset/done/',
        auth_views.PasswordResetDoneView.as_view(
            template_name='gaia_chat/auth/password_reset_done.html'
        ),
        name='password_reset_done'
    ),
    path('password-reset-confirm/<uidb64>/<token>/',
        auth_views.PasswordResetConfirmView.as_view(
            template_name='gaia_chat/auth/password_reset_confirm.html',
            success_url='/gaia_chat/password-reset-complete/'
        ),
        name='password_reset_confirm'
    ),
    path('password-reset-complete/',
        auth_views.PasswordResetCompleteView.as_view(
            template_name='gaia_chat/auth/password_reset_complete.html'
        ),
        name='password_reset_complete'
    ),

    # API endpoints
    path('api/llm/providers/', api.get_llm_providers, name='api_llm_providers'),
    path('api/llm/models/', api.get_models, name='api_llm_models'),
    path('api/llm/default-model/', api.get_default_model, name='api_get_default_model'),
    path('api/llm/set/', api.set_llm, name='api_set_llm'),
    path('api/conversations/', api.list_conversations, name='api_list_conversations'),
    path('api/conversations/create/', api.create_conversation, name='api_create_conversation'),
    path('api/conversations/<str:conversation_id>/', api.load_conversation, name='api_load_conversation'),
    path('api/conversations/<str:conversation_id>/delete/', api.delete_conversation, name='api_delete_conversation'),
    path('api/conversations/<str:conversation_id>/update/', api.update_conversation, name='api_update_conversation'),
    path('api/messages/send/', api.send_message, name='api_send_message'),
    path('api/messages/send-stream/', api.send_message_stream, name='api_send_message_stream'),
    path('api/company-data/', views.get_company_data, name='api_get_company_data'),

    # API endpoint aliases for frontend compatibility
    path('api/chat/', api.send_message, name='api_chat'),  # Alias for send_message
    path('api/chat/stream/', api.chat_stream_get, name='api_chat_stream_get'),  # EventSource streaming endpoint
    path('api/chat/stream/init/', api.chat_stream_init, name='api_chat_stream_init'),  # Stream initialization endpoint

    # Test endpoint (no authentication required)
    path('api/test-progress/', api.test_progress_no_auth, name='api_test_progress'),
]

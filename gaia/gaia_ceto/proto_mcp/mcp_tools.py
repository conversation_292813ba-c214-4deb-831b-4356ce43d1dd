"""
MCP Tools - Shared Functions

This module contains the common tool functions used by both the SSE and HTTP MCP servers.
By centralizing these functions here, we avoid code duplication and ensure consistency
between the different server implementations.
"""

import os
import gaia
from pydantic import Field
from gaia.gaia_frames import gaia_frames
import pandas as pd
import json
import asyncio
import time
from firecrawl import FirecrawlApp

FIRECRAWL_API_KEY = os.environ['FIRECRAWL_API_KEY']


# Import FastMCP Context for type hints (use the same import as debug server)
#from fastmcp import FastMCP, Context

from mcp.server.fastmcp import FastMCP, Context

# Global variable for organization frame columns
org_frame_cols = {}


async def echostring(phrase: str = Field(description="Phrase to echo")) -> str:
    """Echo a string

    Args:
        phrase: the phrase to echo
    """
    return phrase + ", " + phrase + ", " + phrase + " you weasly wabbit... "


async def echostring_table(phrase: str = Field(description="Phrase to echo in table format")) -> str:
    """Echo a string and return it as a table in JSON format

    Args:
        phrase: the phrase to echo in table format

    Returns:
        JSON string containing table data with headers and rows
    """
    # Create sample table data based on the input phrase
    table_data = {
        "type": "table",
        "headers": ["Column 1", "Column 2", "Column 3"],
        "rows": [
            [phrase, f"{phrase}_modified", f"{phrase}_final"],
            ["Row 2 Data", "More data", "Even more"],
            ["Sample", "Table", "Content"],
            [f"Echo: {phrase}", "Repeated", "Again"]
        ],
        "title": f"Echo Table for: {phrase}"
    }

    return json.dumps(table_data)


async def long_task(ctx: Context = None):
    """Long running task with progress + log output."""
    if ctx:
        await ctx.info("//// CONTEXT LONG TASK INITIAL - Starting long task with 5 steps... ////")
    else:
        print("❌ No context injected")

    for i in range(5):
        await asyncio.sleep(1)
        if ctx:
            await ctx.report_progress(i + 1, 5, f"//// CONTEXT PROGRESS - CUSTOM MESSAGE {i + 1} ////")
            await ctx.info(f"//// CONTEXT INFO STEP {i + 1} - ✅ Completed step {i + 1}/5 ////")
            await ctx.info(f"//// ADDITIONAL INFO {i + 1} - Extra info message ////")
        else:
            print(f"📊 !!! No context - Step {i + 1}/5")

    if ctx:
        await ctx.info("🎉 Long task completed successfully!")

    return {"status": "completed", "steps": 5, "message": "Long task finished"}



async def firecrawl_scrape(url: str = Field(description="The URL to scrape")) -> str:
    """Scrape a URL and return the content in markdown format.

    Args:
        url: The URL to scrape
    """
    app = FirecrawlApp(api_key=FIRECRAWL_API_KEY)
    scrape_response = app.scrape_url(url, formats=["markdown", "html"])
    return scrape_response.markdown


async def firecrawl_scrape_text_only(url: str = Field(description="The URL to scrape")) -> str:
    """Scrape a URL and return only the main text content without images or media.

    This is a lightweight version of firecrawl_scrape that focuses on extracting
    clean text content suitable for LLM processing, excluding images, videos,
    and other media elements that can make responses overly long.

    Args:
        url: The URL to scrape
    """
    import re

    app = FirecrawlApp(api_key=FIRECRAWL_API_KEY)
    scrape_response = app.scrape_url(
        url,
        formats=["markdown"],
        only_main_content=True,
        remove_base64_images=True
    )

    # Get the markdown content
    markdown_content = scrape_response.markdown

    # Remove markdown image references: ![alt text](image_url) or ![](image_url)
    # This regex matches ![anything](anything) patterns
    markdown_content = re.sub(r'!\[.*?\]\(.*?\)', '', markdown_content)

    # Remove HTML img tags if any remain: <img src="..." alt="..." />
    markdown_content = re.sub(r'<img[^>]*>', '', markdown_content)

    # Remove iframe tags which can also contain media
    markdown_content = re.sub(r'<iframe[^>]*>.*?</iframe>', '', markdown_content, flags=re.DOTALL)
    markdown_content = re.sub(r'\[iframe\]\([^)]*\)', '', markdown_content)

    # Clean up multiple consecutive newlines (more than 2)
    markdown_content = re.sub(r'\n{3,}', '\n\n', markdown_content)

    # Remove leading/trailing whitespace
    markdown_content = markdown_content.strip()

    return markdown_content



async def get_company_categories_matching(query: str = Field(description="The domain of the company to search for")) -> str:
    """Get a listing of companies matching the search criteria.

    Args:
        query: The domain of the company to search for
    """
    return "Consider only the most closely related categories. " + json.dumps(org_frame_cols)


async def get_llm_completion(sort_criteria: str = Field(description="The criteria for sorting")) -> str:
    """Get a single or small set of matching companies for the (name/domain) search criteria.
    Common sort_criteria:
    total_funding_usd_mm
    num_funding_rounds
    last_funding_on
    founded_on
    """
    co = gaia_frames.read_frame_pandas(
        section_slug='cb',
        frame_slug="organizations",
        cols=['uuid', sort_criteria],
    )
    co = co.sort_values(by=sort_criteria, ascending=False)
    uuids = co.uuid.head(2).to_list()

    uuids_str = [f"'{u}'" for u in uuids]
    co = gaia_frames.read_frame_pandas(
        section_slug='cb',
        frame_slug="organizations",
        where=f"uuid in ({','.join(uuids_str)})",
    )
    co = co.sort_values(by=sort_criteria, ascending=False)
    print(co.head(2))
    return co.head(2).to_string()


async def get_top_companies(sort_criteria: str = Field(description="The criteria for sorting")) -> str:
    """Get a single or small set of matching companies for the (name/domain) search criteria.
    Common sort_criteria:
    total_funding_usd_mm
    num_funding_rounds
    last_funding_on
    founded_on
    """
    co = gaia_frames.read_frame_pandas(
        section_slug='cb',
        frame_slug="organizations",
        cols=['uuid', sort_criteria],
    )
    co = co.sort_values(by=sort_criteria, ascending=False)
    uuids = co.uuid.head(2).to_list()

    uuids_str = [f"'{u}'" for u in uuids]
    co = gaia_frames.read_frame_pandas(
        section_slug='cb',
        frame_slug="organizations",
        where=f"uuid in ({','.join(uuids_str)})",
    )
    co = co.sort_values(by=sort_criteria, ascending=False)
    print(co.head(2))
    return co.head(2).to_string()


async def get_company_listing(
    domain: str = Field(description="The domain of the company to search for", default=None),
    name: str = Field(description="The name of the company to search for", default=None)
) -> str:
    """Get a single or small set of matching companies for the (name/domain) search criteria.

    Args:
        domain: The domain of the company to search for
        name: The name of the company to search for
    """
    where_clauses = []
    if domain:
        where_clauses.append(f"domain='{domain}'")
    if name:
        where_clauses.append(f"name like '{name}%'")

    co = gaia_frames.read_frame_pandas(
        section_slug='cb',
        frame_slug="organizations",
        where=' and '.join(where_clauses)
    )
    print(co.head())
    return co.to_string()


async def get_json_table() -> str:
    """Generate a JSON-formatted table with 6 rows by 6 columns of dummy data.

    This tool returns raw JSON table content with a mix of strings and numbers
    to test the showToolOutput functionality and demonstrate how raw output
    can be useful for certain tools.

    Returns:
        JSON string containing a formatted table with dummy data
    """
    table_data = {
        "type": "table",
        "title": "Product Inventory Report",
        "columns": ["Product", "Price ($)", "Quantity", "Category", "Rating", "In Stock"],
        "rows": [
            ["Laptop Pro", 1299.99, 45, "Electronics", 4.8, "Yes"],
            ["Wireless Mouse", 29.95, 120, "Accessories", 4.2, "Yes"],
            ["Office Chair", 249.00, 8, "Furniture", 4.5, "No"],
            ["Coffee Maker", 89.99, 32, "Appliances", 4.1, "Yes"],
            ["Smartphone", 699.00, 67, "Electronics", 4.6, "Yes"],
            ["Desk Lamp", 34.50, 15, "Lighting", 3.9, "Yes"]
        ],
        "metadata": {
            "total_rows": 6,
            "total_columns": 6,
            "generated_at": "2025-07-15",
            "data_types": {
                "Product": "string",
                "Price ($)": "float",
                "Quantity": "integer",
                "Category": "string",
                "Rating": "float",
                "In Stock": "string"
            }
        }
    }

    return json.dumps(table_data, indent=2)


async def simple_json_table() -> str:
    """Generate a JSON-formatted table with 6 rows by 6 columns of dummy data.

    This tool returns raw JSON table content with a mix of strings and numbers
    to test the showToolOutput functionality and demonstrate how raw output
    can be useful for certain tools.

    Returns:
        JSON string containing a formatted table with dummy data
    """
    table_data = {
        "type": "table",
        "title": "simple_json_table",
        "columns": ["id", "name", "dept", "salary", "active", "start_date"],
        "rows": [
            [1, "Alice",  "Sales",       70000,  'true',  "2024-11-01"],
            [2, "Bob",    "Engineering", 80000,  'true',  "2024-12-13"],
            [3, "Carol",  "HR",          60000,  'false', "2025-01-07"],
            [4, "Dave",   "Marketing",   65000,  'true',  "2025-02-20"],
            [5, "Eve",    "Finance",     90000,  'false', "2025-03-15"],
            [6, "Frank",  "Support",     50000,  'true',  "2025-04-08"]
        ]
    }
    return json.dumps(table_data, indent=2)
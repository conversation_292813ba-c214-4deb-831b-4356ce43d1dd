#!/usr/bin/env python3
"""
Test script to verify the centralized model configuration works correctly.
"""

import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_model_config():
    """Test the centralized model configuration."""
    print("Testing centralized model configuration...")
    
    try:
        from gaia.gaia_llm.model_config import (
            model_registry, 
            get_anthropic_models, 
            get_openai_models, 
            get_mock_models,
            get_default_model,
            DEFAULT_ANTHROPIC_MODEL,
            DEFAULT_OPENAI_MODEL,
            DEFAULT_MOCK_MODEL
        )
        
        print("✓ Successfully imported model configuration")
        
        # Test Anthropic models
        anthropic_models = get_anthropic_models()
        print(f"✓ Anthropic models ({len(anthropic_models)}): {anthropic_models[:3]}...")
        
        # Test OpenAI models
        openai_models = get_openai_models()
        print(f"✓ OpenAI models ({len(openai_models)}): {openai_models[:3]}...")
        
        # Test Mock models
        mock_models = get_mock_models()
        print(f"✓ Mock models ({len(mock_models)}): {mock_models}")
        
        # Test default models
        print(f"✓ Default Anthropic model: {DEFAULT_ANTHROPIC_MODEL}")
        print(f"✓ Default OpenAI model: {DEFAULT_OPENAI_MODEL}")
        print(f"✓ Default Mock model: {DEFAULT_MOCK_MODEL}")
        
        # Test model choices (with display names)
        anthropic_choices = model_registry.get_model_choices_by_provider("anthropic")
        print(f"✓ Anthropic model choices: {anthropic_choices[:2]}...")
        
        # Test model info
        model_info = model_registry.get_model_info(DEFAULT_ANTHROPIC_MODEL)
        if model_info:
            print(f"✓ Model info for {DEFAULT_ANTHROPIC_MODEL}: {model_info.name} - {model_info.description}")
        
        print("\n✅ All tests passed! Centralized model configuration is working correctly.")
        return True
        
    except Exception as e:
        print(f"❌ Error testing model configuration: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration():
    """Test integration with existing components."""
    print("\nTesting integration with existing components...")
    
    try:
        # Test chatobj integration
        from gaia.gaia_ceto.ceto_v002.chatobj import AnthropicLLM, OpenAILLM
        
        anthropic_models = AnthropicLLM.get_available_models()
        print(f"✓ AnthropicLLM.get_available_models(): {anthropic_models[:2]}...")
        
        openai_models = OpenAILLM.get_available_models()
        print(f"✓ OpenAILLM.get_available_models(): {openai_models[:2]}...")
        
        # Test MCP client integration
        try:
            from gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib import DEFAULT_MODEL
            print(f"✓ MCP HTTP client default model: {DEFAULT_MODEL}")
        except ImportError:
            print("⚠ MCP HTTP client not available for testing")
        
        try:
            from gaia.gaia_ceto.proto_mcp.mcp_sse_clientlib import DEFAULT_MODEL as SSE_DEFAULT_MODEL
            print(f"✓ MCP SSE client default model: {SSE_DEFAULT_MODEL}")
        except ImportError:
            print("⚠ MCP SSE client not available for testing")
        
        print("✅ Integration tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing integration: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("CENTRALIZED MODEL CONFIGURATION TEST")
    print("=" * 60)
    
    success = test_model_config()
    if success:
        success = test_integration()
    
    if success:
        print("\n🎉 All tests completed successfully!")
        print("The centralized model configuration is working correctly.")
    else:
        print("\n💥 Some tests failed. Please check the errors above.")
        sys.exit(1)
